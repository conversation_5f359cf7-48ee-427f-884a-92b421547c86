import app from './app';
import { config } from './config';
import fs from 'fs';
import https from 'https';
import http from 'http';
import { logger } from './utils/logger';

// Import seed data for tournaments
import './features/tournaments/services/seedTournaments';

// Use the port defined in the config object
const PORT = process.env.PORT || 8080; // Use environment variable or default to 8080

// Start the server with appropriate protocol based on environment
if (process.env.USE_HTTPS === 'true' && process.env.NODE_ENV === 'production') {
  try {
    // Check if SSL certificate files exist
    const sslKeyPath = process.env.SSL_KEY_PATH;
    const sslCertPath = process.env.SSL_CERT_PATH;
    
    if (!sslKeyPath || !sslCertPath) {
      throw new Error('SSL certificate paths not provided in environment variables');
    }
    
    // SSL options for HTTPS
    const httpsOptions = {
      key: fs.readFileSync(sslKeyPath),
      cert: fs.readFileSync(sslCertPath)
    };
    
    // Create HTTPS server
    const server = https.createServer(httpsOptions, app);
    
    server.listen(PORT, () => {
      logger.info(`Secure WiggyZ server running on port ${PORT} in ${config.server.env} mode (HTTPS)`);
      logger.info(`Server accessible at: http://192.168.1.97:${PORT}`);
    });
  } catch (error) {
    logger.error(`Failed to start HTTPS server: ${error instanceof Error ? error.message : String(error)}`);
    
    // Fallback to HTTP if HTTPS fails (not recommended for production)
    logger.warn('Falling back to HTTP server - THIS IS NOT SECURE FOR PRODUCTION');
    const server = http.createServer(app);
    
    server.listen(PORT, () => {
      logger.warn(`Fallback insecure WiggyZ server running on port ${PORT} in ${config.server.env} mode (HTTP)`);
      logger.info(`Server accessible at: http://192.168.1.97:${PORT}`);
    });
  }
} else {
  // Use HTTP for development
  const server = http.createServer(app);
  
  server.listen(PORT, () => {
    logger.info(`WiggyZ server running on port ${PORT} in ${config.server.env} mode (HTTP)`);
    logger.info(`Server accessible at: http://192.168.1.97:${PORT}`);
  });
}
